import os
import torch
import argparse
from datetime import datetime
from config import Logger, seed_torch, worker_init_fn, entropy_minimization_loss
from utils.convert import AdaBN
from utils.metrics import calculate_metrics
from utils.loss_debugger import CoDGPDebugger
from utils.covariance_utils import covariance_alignment_loss, safe_load_source_covariance, compute_covariance_matrix
from networks.ResUnet_TTA import ResUnet
from torch.utils.data import DataLoader
from dataloaders.OPTIC_dataloader import OPTIC_dataset
from dataloaders.transform import collate_fn_wo_transform
from dataloaders.convert_csv_to_list import convert_labeled_list


torch.set_num_threads(1)


class VPTTA:
    """
    Co-DGP (Covariance-aware Dynamic Graph Prompting) 测试时适应框架

    使用图注意力网络建模通道间关系，通过协方差对齐实现域适应。
    结合AdaBN、协方差对齐损失和熵最小化损失进行测试时适应。
    """

    def __init__(self, config):
        # 立即设置随机种子，确保模型初始化的可重现性
        seed_torch(config.seed)

        # Save Log
        time_now = datetime.now().__format__("%Y%m%d_%H%M%S_%f")
        log_root = os.path.join(config.path_save_log, 'VPTTA')
        if not os.path.exists(log_root):
            os.makedirs(log_root)
        log_path = os.path.join(log_root, time_now + '.log')
        import sys
        sys.stdout = Logger(log_path, sys.stdout)

        # Data Loading
        target_test_csv = []
        for target in config.Target_Dataset:
            if target != 'REFUGE_Valid':
                target_test_csv.append(target + '_train.csv')
                target_test_csv.append(target + '_test.csv')
            else:
                target_test_csv.append(target + '.csv')
        ts_img_list, ts_label_list = convert_labeled_list(config.dataset_root, target_test_csv)
        target_test_dataset = OPTIC_dataset(config.dataset_root, ts_img_list, ts_label_list,
                                            config.image_size, img_normalize=True)

        # 创建worker初始化函数，捕获当前种子值
        worker_init = lambda worker_id: worker_init_fn(worker_id, config.seed)

        self.target_test_loader = DataLoader(dataset=target_test_dataset,
                                             batch_size=config.batch_size,
                                             shuffle=False,
                                             pin_memory=True,
                                             drop_last=False,
                                             collate_fn=collate_fn_wo_transform,
                                             num_workers=config.num_workers,
                                             worker_init_fn=worker_init)
        self.image_size = config.image_size

        # Model
        self.load_model = os.path.join(config.model_root, str(config.Source_Dataset))  # Pre-trained Source Model
        self.backbone = config.backbone
        self.in_ch = config.in_ch
        self.out_ch = config.out_ch

        # GPU
        self.device = config.device

        # Warm-up
        self.warm_n = config.warm_n

        # Prompt
        self.prompt_alpha = config.prompt_alpha
        self.iters = config.iters

        # Entropy minimization
        self.entropy_weight = config.entropy_weight
        self.entropy_temperature = config.entropy_temperature

        # 保存Co-DGP相关的配置
        self.codgp_lr = config.codgp_lr
        self.gat_hidden_dim = getattr(config, 'gat_hidden_dim', 128)
        self.gat_num_heads = getattr(config, 'gat_num_heads', 4)
        self.gat_num_layers = getattr(config, 'gat_num_layers', 2)
        self.gat_dropout = getattr(config, 'gat_dropout', 0.1)
        self.cov_loss_weight = getattr(config, 'cov_loss_weight', 1.0)

        # 初始化调试器
        self.debug_loss = getattr(config, 'debug_loss', False)
        debug_log_file = None
        if self.debug_loss:
            # 创建调试日志文件路径
            time_now = datetime.now().__format__("%Y%m%d_%H%M%S_%f")
            debug_log_file = os.path.join(config.path_save_log, 'VPTTA', f'debug_loss_{time_now}.log')
        self.loss_debugger = CoDGPDebugger(enabled=self.debug_loss, log_file=debug_log_file)

        # Initialize the pre-trained model and optimizer
        self.build_model()

        # Print Information
        if self.debug_loss:
            print("=== Configuration Details ===")
            for arg, value in vars(config).items():
                print(f"{arg}: {value}")
        else:
            # 简洁的基础信息
            print(f"VPTTA Config: {config.Source_Dataset} -> {config.Target_Dataset}")
            print(f"Model: {config.backbone} | Device: {config.device} | Seed: {config.seed}")
        print('***' * 20)

    def build_model(self):
        # 加载主模型并集成Co-DGP
        self.model = ResUnet(
            resnet=self.backbone,
            num_classes=self.out_ch,
            pretrained=False,
            newBN=AdaBN,
            warm_n=self.warm_n,
            gat_hidden_dim=self.gat_hidden_dim,
            gat_num_heads=self.gat_num_heads,
            gat_num_layers=self.gat_num_layers,
            gat_dropout=self.gat_dropout
        ).to(self.device)
        checkpoint = torch.load(os.path.join(self.load_model, 'last-Res_Unet.pth'))
        self.model.load_state_dict(checkpoint, strict=False)  # strict=False因为新增了Co-DGP模块

        # 安全加载源域协方差矩阵
        covariance_path = os.path.join(self.load_model, 'source_covariance.pt')
        self.source_covariance = safe_load_source_covariance(covariance_path, self.device)
        self.has_source_covariance = self.source_covariance is not None

        # 打印协方差矩阵状态信息
        if self.has_source_covariance:
            print(f"✓ Co-DGP: Full functionality enabled (Graph Attention + Covariance Alignment)")
            print(f"  Source covariance shape: {self.source_covariance.shape}")
        else:
            print(f"⚠ Co-DGP: Partial functionality (Graph Attention only, Covariance Alignment disabled)")
            print(f"  To enable full functionality, place 'source_covariance.pt' in: {self.load_model}")

        # 创建参数组，只有Co-DGP模块参数需要优化
        param_groups = [
            {'params': self.model.codgp.parameters(), 'lr': self.codgp_lr}
        ]

        self.optimizer = torch.optim.Adam(param_groups)

        # 验证Co-DGP配置
        if self.debug_loss:
            print("=== Co-DGP Debug Information ===")
            print(f"Co-DGP module type: {type(self.model.codgp).__name__}")
            print(f"Architecture: Graph Attention Network")
            print(f"GAT hidden dim: {self.gat_hidden_dim}")
            print(f"GAT num heads: {self.gat_num_heads}")
            print(f"GAT num layers: {self.gat_num_layers}")
            print(f"GAT dropout: {self.gat_dropout}")
            print(f"Loss composition: BN(1.0) + Cov({self.cov_loss_weight}) + Entropy({self.entropy_weight})")
            print(f"Trainable parameters: {sum(p.numel() for p in self.model.codgp.parameters() if p.requires_grad)}")
            print(f"Optimizer groups: {len(self.optimizer.param_groups)}")
            print(f"Entropy temperature: {self.entropy_temperature}")
            print(f"Source covariance shape: {self.source_covariance.shape}")
            print("=== End Co-DGP Debug Information ===")



    def run(self):
        """
        执行Co-DGP测试时适应

        对每个目标域样本：
        1. 使用Co-DGP进行特征调制
        2. 计算三项损失：BN损失 + 协方差对齐损失 + 熵损失
        3. 优化Co-DGP参数
        4. 进行最终推理和指标计算
        """
        metric_dict = ['Disc_Dice', 'Disc_ASD', 'Cup_Dice', 'Cup_ASD']

        # Valid on Target
        metrics_test = [[], [], [], []]

        for batch, data in enumerate(self.target_test_loader):
            x, y = data['data'], data['mask']
            x = torch.from_numpy(x).to(dtype=torch.float32)
            y = torch.from_numpy(y).to(dtype=torch.float32)

            x, y = x.to(self.device), y.to(self.device)

            self.model.eval()
            self.model.change_BN_status(new_sample=True)

            # 训练Co-DGP模块进行域适应 (每个样本1次迭代)
            for tr_iter in range(self.iters):
                # --- 1. 前向传播（Co-DGP自动应用） ---
                seg_output, sfs, _, _ = self.model(x)

                # --- 2. 计算三项损失 ---
                # 计算BN损失（权重固定为1.0）
                times, bn_loss = 0, 0
                for nm, m in self.model.named_modules():
                    if isinstance(m, AdaBN):
                        bn_loss += m.bn_loss
                        times += 1
                bn_loss = bn_loss / times if times > 0 else 0.0

                # 计算协方差对齐损失
                layer3_features = sfs[2]  # Co-DGP调制后的layer3特征
                cov_loss = covariance_alignment_loss(layer3_features, self.source_covariance)

                # 计算熵最小化损失
                entropy_loss = entropy_minimization_loss(seg_output, self.entropy_temperature)

                # 组合损失：BN损失 + 协方差损失（如果可用）+ 熵最小化损失
                total_loss = bn_loss + self.entropy_weight * entropy_loss
                if self.has_source_covariance:
                    total_loss += self.cov_loss_weight * cov_loss

                # --- 3. 收集调试数据 ---
                if self.debug_loss:
                    # 保存调制前的特征用于对比
                    pre_modulation_features = sfs[2].clone()  # 调制前的layer3特征

                    # 1. 损失信息
                    losses_dict = {
                        'bn_loss': float(bn_loss),
                        'cov_loss': float(cov_loss),
                        'entropy_loss': float(entropy_loss),
                        'total_loss': float(total_loss)
                    }

                    # 2. 调制参数信息
                    modulation_stats = self.model.codgp.get_modulation_stats(layer3_features)

                    # 3. 协方差矩阵信息
                    target_cov = compute_covariance_matrix(layer3_features)
                    if self.has_source_covariance:
                        covariance_dict = self.loss_debugger.monitor_covariance_health(target_cov, self.source_covariance)
                    else:
                        covariance_dict = {'status': 'source_covariance_unavailable'}

                    # 4. 注意力模式信息
                    attention_weights = self.model.codgp.get_attention_weights(layer3_features)
                    attention_dict = self.loss_debugger.monitor_attention_patterns(attention_weights)

                    # 5. 特征分布信息
                    features_dict = self.loss_debugger.monitor_feature_distribution(
                        pre_modulation_features, layer3_features
                    )

                # --- 7. 反向传播与优化 ---
                self.optimizer.zero_grad()
                total_loss.backward()

                # 6. 梯度信息收集
                if self.debug_loss:
                    total_grad_norm = 0.0
                    max_grad_value = 0.0
                    param_count = 0
                    has_grad = False

                    for param in self.model.codgp.parameters():
                        if param.grad is not None:
                            param_grad_norm = param.grad.data.norm(2)
                            total_grad_norm += param_grad_norm.item() ** 2
                            max_grad_value = max(max_grad_value, param.grad.data.abs().max().item())
                            param_count += 1
                            has_grad = True

                    if has_grad and param_count > 0:
                        total_grad_norm = (total_grad_norm ** 0.5)
                        if total_grad_norm < 1e-6:
                            grad_status = 'VANISH'
                        elif total_grad_norm > 10.0:
                            grad_status = 'EXPLODE'
                        else:
                            grad_status = 'NORMAL'
                    else:
                        total_grad_norm = 0.0
                        max_grad_value = 0.0
                        grad_status = 'NO_GRAD'

                    gradients_dict = {
                        'norm': total_grad_norm,
                        'status': grad_status,
                        'max_grad': max_grad_value
                    }

                self.optimizer.step()
                self.model.change_BN_status(new_sample=False)

            # 推理阶段：使用训练后的Co-DGP进行预测
            self.model.eval()
            with torch.no_grad():
                # Co-DGP直接处理原始输入
                pred_logit, fea, head_input, pooled_x4 = self.model(x)

            # Calculate the metrics
            seg_output = torch.sigmoid(pred_logit)

            metrics = calculate_metrics(seg_output.detach().cpu(), y.detach().cpu())
            for i in range(len(metrics)):
                assert isinstance(metrics[i], list), "The metrics value is not list type."
                metrics_test[i] += metrics[i]

            # 记录完整的调试信息（损失+DICE分数）
            if self.debug_loss:
                # 计算当前样本的平均DICE分数
                disc_dice = sum(metrics[0]) / len(metrics[0]) if metrics[0] else 0.0
                cup_dice = sum(metrics[2]) / len(metrics[2]) if metrics[2] else 0.0

                # 7. 性能指标信息
                metrics_dict = {
                    'disc_dice': float(disc_dice),
                    'cup_dice': float(cup_dice)
                }

                # 统一调用综合调试日志
                self.loss_debugger.log_comprehensive_debug(
                    sample_idx=batch + 1,
                    iter_idx=1,  # 对应训练的迭代
                    losses_dict=losses_dict,
                    modulation_dict=modulation_stats,
                    covariance_dict=covariance_dict,
                    attention_dict=attention_dict,
                    gradients_dict=gradients_dict,
                    features_dict=features_dict,
                    metrics_dict=metrics_dict
                )

        test_metrics_y = [sum(metric_list) / len(metric_list) for metric_list in metrics_test]
        print_test_metric_mean = {}
        for i in range(len(test_metrics_y)):
            print_test_metric_mean[metric_dict[i]] = test_metrics_y[i]
        print("Test Metrics: ", print_test_metric_mean)
        print('Mean Dice:', (print_test_metric_mean['Disc_Dice'] + print_test_metric_mean['Cup_Dice']) / 2)

        # 关闭调试器
        if self.debug_loss:
            self.loss_debugger.close()


if __name__ == '__main__':
    parser = argparse.ArgumentParser()
    # Dataset
    parser.add_argument('--Source_Dataset', type=str, default='RIM_ONE_r3',
                        help='RIM_ONE_r3/REFUGE/ORIGA/REFUGE_Valid/Drishti_GS')
    parser.add_argument('--Target_Dataset', type=list)

    parser.add_argument('--num_workers', type=int, default=8)
    parser.add_argument('--image_size', type=int, default=512)

    # Model
    parser.add_argument('--backbone', type=str, default='resnet34', help='resnet34/resnet50')
    parser.add_argument('--in_ch', type=int, default=3)
    parser.add_argument('--out_ch', type=int, default=2)

    # Training
    parser.add_argument('--batch_size', type=int, default=1)
    parser.add_argument('--iters', type=int, default=1)

    # Hyperparameters in prompt and warm-up statistics
    parser.add_argument('--prompt_alpha', type=float, default=0.01)
    parser.add_argument('--warm_n', type=int, default=5)

    # Entropy minimization parameters
    parser.add_argument('--entropy_weight', type=float, default=0.1,
                        help='Weight for entropy minimization loss')
    parser.add_argument('--entropy_temperature', type=float, default=1.0,
                        help='Temperature parameter for entropy calculation')

    # Path
    parser.add_argument('--path_save_log', type=str, default='./logs')
    parser.add_argument('--model_root', type=str, default='./models')
    parser.add_argument('--dataset_root', type=str, default='/media/userdisk0/zychen/Datasets/Fundus')

    # Cuda (default: the first available device)
    parser.add_argument('--device', type=str, default='cuda:0')

    # 超网络参数
    parser.add_argument('--generator_lr', type=float, default=0.0001,
                        help='Learning rate for the prompt generator.')

    # Co-DGP参数
    parser.add_argument('--gat_hidden_dim', type=int, default=128,
                        help='GAT hidden layer dimension.')
    parser.add_argument('--gat_num_heads', type=int, default=4,
                        help='Number of attention heads in GAT.')
    parser.add_argument('--gat_num_layers', type=int, default=2,
                        help='Number of GAT layers.')
    parser.add_argument('--gat_dropout', type=float, default=0.1,
                        help='Dropout rate for GAT.')
    parser.add_argument('--cov_loss_weight', type=float, default=1.0,
                        help='Weight for covariance alignment loss.')
    parser.add_argument('--codgp_lr', type=float, default=0.001,
                        help='Learning rate for Co-DGP module.')

    # 调试相关参数
    parser.add_argument('--debug_loss', action='store_true',
                        help='Enable loss and performance debugging output.')

    # 可重现性相关参数
    parser.add_argument('--seed', type=int, default=42,
                        help='Random seed for reproducible model initialization.')

    config = parser.parse_args()

    config.Target_Dataset = ['RIM_ONE_r3', 'REFUGE', 'ORIGA', 'REFUGE_Valid', 'Drishti_GS']
    config.Target_Dataset.remove(config.Source_Dataset)

    TTA = VPTTA(config)
    TTA.run()
