import torch
import torch.nn as nn
import torch.nn.functional as F
from .graph_attention import GAT


class CoDGPModule(nn.Module):
    """
    Co-DGP (Covariance-aware Dynamic Graph Prompting) 模块
    
    核心功能：
    1. 将特征通道建模为图节点
    2. 使用GAT学习通道间关系
    3. 生成通道调制参数进行自适应特征变换
    """
    
    def __init__(self, num_channels, gat_hidden_dim=128, gat_num_heads=4, 
                 gat_num_layers=2, gat_dropout=0.1):
        """
        初始化Co-DGP模块
        
        Args:
            num_channels (int): 输入特征通道数
            gat_hidden_dim (int): GAT隐藏层维度
            gat_num_heads (int): GAT注意力头数
            gat_num_layers (int): GAT层数
            gat_dropout (float): GAT dropout率
        """
        super(CoDGPModule, self).__init__()
        self.num_channels = num_channels
        self.gat_hidden_dim = gat_hidden_dim
        
        # 通道特征投影层：将全局平均池化后的通道特征投影到GAT输入维度
        self.channel_projection = nn.Sequential(
            nn.Linear(1, gat_hidden_dim // 2),
            nn.ReLU(inplace=True),
            nn.Linear(gat_hidden_dim // 2, gat_hidden_dim),
            nn.ReLU(inplace=True)
        )
        
        # 图注意力网络：学习通道间关系
        self.gat = GAT(
            input_dim=gat_hidden_dim,
            hidden_dim=gat_hidden_dim,
            output_dim=gat_hidden_dim,
            num_heads=gat_num_heads,
            num_layers=gat_num_layers,
            dropout=gat_dropout
        )
        
        # 通道调制参数预测头
        self.modulation_head = nn.Sequential(
            nn.Linear(gat_hidden_dim, gat_hidden_dim // 2),
            nn.ReLU(inplace=True),
            nn.Linear(gat_hidden_dim // 2, 2)  # 输出gamma和beta
        )
        
        # 初始化权重
        self._initialize_weights()
    
    def _initialize_weights(self):
        """初始化模块权重"""
        for m in self.modules():
            if isinstance(m, nn.Linear):
                nn.init.xavier_uniform_(m.weight)
                if m.bias is not None:
                    nn.init.zeros_(m.bias)
        
        # 调制参数的特殊初始化：gamma初始化为1，beta初始化为0
        with torch.no_grad():
            # 最后一层的权重和偏置特殊处理
            final_layer = self.modulation_head[-1]
            # gamma部分（前半部分）初始化为接近1的值
            final_layer.bias[0::2] = 1.0  # gamma初始化为1
            final_layer.bias[1::2] = 0.0  # beta初始化为0
            
            # 权重初始化为小值，确保初始时调制接近恒等变换
            nn.init.normal_(final_layer.weight, mean=0.0, std=0.01)
    
    def forward(self, features):
        """
        前向传播
        
        Args:
            features (torch.Tensor): 输入特征 (B, C, H, W)
            
        Returns:
            torch.Tensor: 调制后的特征 (B, C, H, W)
        """
        B, C, H, W = features.shape
        
        # 1. 构建图节点表示：每个通道作为一个节点
        # 对每个通道进行全局平均池化
        channel_features = F.adaptive_avg_pool2d(features, (1, 1))  # (B, C, 1, 1)
        channel_features = channel_features.view(B, C, 1)  # (B, C, 1)
        
        # 投影到GAT输入空间
        node_features = self.channel_projection(channel_features)  # (B, C, gat_hidden_dim)
        
        # 2. 对每个样本独立应用GAT
        modulated_features_list = []
        
        for b in range(B):
            # 提取单个样本的节点特征
            sample_nodes = node_features[b]  # (C, gat_hidden_dim)
            
            # 应用GAT学习通道间关系
            enhanced_nodes = self.gat(sample_nodes)  # (C, gat_hidden_dim)
            
            # 生成调制参数
            modulation_params = self.modulation_head(enhanced_nodes)  # (C, 2)
            gamma = modulation_params[:, 0].unsqueeze(0).unsqueeze(-1).unsqueeze(-1)  # (1, C, 1, 1)
            beta = modulation_params[:, 1].unsqueeze(0).unsqueeze(-1).unsqueeze(-1)   # (1, C, 1, 1)
            
            # 应用通道调制
            sample_features = features[b:b+1]  # (1, C, H, W)
            modulated_sample = gamma * sample_features + beta  # (1, C, H, W)
            modulated_features_list.append(modulated_sample)
        
        # 合并所有样本
        modulated_features = torch.cat(modulated_features_list, dim=0)  # (B, C, H, W)
        
        return modulated_features
    
    def get_attention_weights(self, features):
        """
        获取注意力权重用于可视化分析
        
        Args:
            features (torch.Tensor): 输入特征 (B, C, H, W)
            
        Returns:
            list: 每个样本的注意力权重矩阵列表
        """
        B, C, H, W = features.shape
        
        # 构建节点表示
        channel_features = F.adaptive_avg_pool2d(features, (1, 1))
        channel_features = channel_features.view(B, C, 1)
        node_features = self.channel_projection(channel_features)
        
        attention_weights_list = []
        
        for b in range(B):
            sample_nodes = node_features[b]
            
            # 这里需要修改GAT以返回注意力权重
            # 暂时返回None，实际使用时需要扩展GAT的forward方法
            attention_weights_list.append(None)
        
        return attention_weights_list
    
    def get_modulation_stats(self, features):
        """
        获取调制参数的统计信息用于调试
        
        Args:
            features (torch.Tensor): 输入特征 (B, C, H, W)
            
        Returns:
            dict: 调制参数统计信息
        """
        B, C, H, W = features.shape
        
        channel_features = F.adaptive_avg_pool2d(features, (1, 1))
        channel_features = channel_features.view(B, C, 1)
        node_features = self.channel_projection(channel_features)
        
        all_gammas = []
        all_betas = []
        
        for b in range(B):
            sample_nodes = node_features[b]
            enhanced_nodes = self.gat(sample_nodes)
            modulation_params = self.modulation_head(enhanced_nodes)
            
            gamma = modulation_params[:, 0]
            beta = modulation_params[:, 1]
            
            all_gammas.append(gamma)
            all_betas.append(beta)
        
        all_gammas = torch.stack(all_gammas)  # (B, C)
        all_betas = torch.stack(all_betas)    # (B, C)
        
        stats = {
            'gamma_mean': all_gammas.mean().item(),
            'gamma_std': all_gammas.std().item(),
            'gamma_min': all_gammas.min().item(),
            'gamma_max': all_gammas.max().item(),
            'beta_mean': all_betas.mean().item(),
            'beta_std': all_betas.std().item(),
            'beta_min': all_betas.min().item(),
            'beta_max': all_betas.max().item(),
        }
        
        return stats

    def get_attention_weights(self, features):
        """
        提取GAT注意力权重用于调试

        Args:
            features (torch.Tensor): 输入特征 (B, C, H, W)

        Returns:
            list: 各层注意力权重列表
        """
        B, C, H, W = features.shape

        channel_features = F.adaptive_avg_pool2d(features, (1, 1))
        channel_features = channel_features.view(B, C, 1)
        node_features = self.channel_projection(channel_features)

        # 提取第一个样本的注意力权重进行分析
        sample_nodes = node_features[0]  # (C, hidden_dim)

        # 这里需要修改GAT的forward方法来返回注意力权重
        # 暂时返回None，实际实现时需要扩展GAT
        attention_weights_list = []

        # 调用GAT并获取注意力权重
        enhanced_nodes = self.gat(sample_nodes)

        # 暂时返回None，实际使用时需要扩展GAT的forward方法
        attention_weights_list.append(None)

        return attention_weights_list


class CoDGPResUnetIntegration(nn.Module):
    """
    Co-DGP与ResUnet的集成模块
    
    在layer3输出后插入Co-DGP模块进行特征调制
    """
    
    def __init__(self, original_resunet, gat_hidden_dim=128, gat_num_heads=4, 
                 gat_num_layers=2, gat_dropout=0.1):
        """
        初始化集成模块
        
        Args:
            original_resunet: 原始的ResUnet模型
            gat_hidden_dim (int): GAT隐藏层维度
            gat_num_heads (int): GAT注意力头数
            gat_num_layers (int): GAT层数
            gat_dropout (float): GAT dropout率
        """
        super(CoDGPResUnetIntegration, self).__init__()
        
        # 保存原始ResUnet的组件
        self.res = original_resunet.res
        self.up1 = original_resunet.up1
        self.up2 = original_resunet.up2
        self.up3 = original_resunet.up3
        self.up4 = original_resunet.up4
        self.up5 = original_resunet.up5
        self.bnout = original_resunet.bnout
        self.seg_head = original_resunet.seg_head
        self.num_classes = original_resunet.num_classes
        self.newBN = original_resunet.newBN
        
        # 保存特征hooks（如果存在）
        if hasattr(original_resunet, 'feature_hooks'):
            self.feature_hooks = original_resunet.feature_hooks
        
        # 初始化Co-DGP模块（针对layer3的256通道）
        self.codgp = CoDGPModule(
            num_channels=256,  # layer3输出通道数
            gat_hidden_dim=gat_hidden_dim,
            gat_num_heads=gat_num_heads,
            gat_num_layers=gat_num_layers,
            gat_dropout=gat_dropout
        )
    
    def forward(self, x):
        """
        前向传播，在layer3后插入Co-DGP
        
        Args:
            x (torch.Tensor): 输入图像 (B, 3, H, W)
            
        Returns:
            tuple: (seg_output, sfs, head_input, pooled_x4)
        """
        # ResNet编码器前向传播
        x, sfs = self.res(x)
        
        # 在layer3输出后应用Co-DGP
        # sfs[2]是layer3的输出，sfs[3]是layer4的输出
        layer3_output = sfs[2]  # (B, 256, H/8, W/8)
        
        # 应用Co-DGP进行特征调制
        modulated_layer3 = self.codgp(layer3_output)
        
        # 更新sfs中的layer3特征
        sfs[2] = modulated_layer3
        
        # 提取layer4池化特征用于特征提取器
        pooled_x4 = F.adaptive_avg_pool2d(x, (1, 1)).view(x.size(0), -1)
        x = F.relu(x)
        
        # 解码器前向传播
        x = self.up1(x, sfs[3])
        x = self.up2(x, sfs[2])  # 使用调制后的layer3特征
        x = self.up3(x, sfs[1])
        x = self.up4(x, sfs[0])
        x = self.up5(x)
        head_input = F.relu(self.bnout(x))
        
        seg_output = self.seg_head(head_input)
        
        return seg_output, sfs, head_input, pooled_x4
    
    def change_BN_status(self, new_sample=True):
        """更改BN状态"""
        for nm, m in self.named_modules():
            if isinstance(m, self.newBN):
                m.new_sample = new_sample
    
    def reset_sample_num(self):
        """重置样本数量"""
        for nm, m in self.named_modules():
            if isinstance(m, self.newBN):
                m.new_sample = 0
