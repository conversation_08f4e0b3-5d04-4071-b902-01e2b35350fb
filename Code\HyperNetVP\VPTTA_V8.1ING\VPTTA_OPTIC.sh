
#!/bin/bash

#Please modify the following roots to yours.
dataset_root=/opt/data/private/zjw/Data/Fundus
model_root=/opt/data/private/zjw/Data/models
path_save_log=/opt/data/private/zjw/VPTTA-main/OPTIC/logs/


#Dataset [RIM_ONE_r3, REFUGE, ORIGA, REF<PERSON>GE_Valid, Drishti_GS]
Source=ORIGA

#Hyperparameters
prompt_alpha=0.01
warm_n=5

# Co-DGP Parameters
codgp_lr=0.001
gat_hidden_dim=128
gat_num_heads=4
gat_num_layers=2
gat_dropout=0.1
cov_loss_weight=1.0

# Entropy minimization parameters
entropy_weight=0.5
entropy_temperature=1.0

#Debug Parameters
enable_debug=true

#Reproducibility Parameters
seed=42

#Command
cd OPTIC

# 构建调试参数
debug_args=""
if [ "$enable_debug" = "true" ]; then
    debug_args="--debug_loss"
fi



CUDA_VISIBLE_DEVICES=0 python vptta.py \
--dataset_root $dataset_root --model_root $model_root --path_save_log $path_save_log \
--Source_Dataset $Source \
--prompt_alpha $prompt_alpha --warm_n $warm_n \
--codgp_lr $codgp_lr \
--gat_hidden_dim $gat_hidden_dim \
--gat_num_heads $gat_num_heads \
--gat_num_layers $gat_num_layers \
--gat_dropout $gat_dropout \
--cov_loss_weight $cov_loss_weight \
--entropy_weight $entropy_weight \
--entropy_temperature $entropy_temperature \
--seed $seed \
$debug_args