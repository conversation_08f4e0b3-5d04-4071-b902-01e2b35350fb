import torch
import torch.nn.functional as F
import os


def compute_covariance_matrix(features):
    """
    计算特征的协方差矩阵
    
    Args:
        features (torch.Tensor): 特征张量 (B, C, H, W) 或 (N, C, HW)
        
    Returns:
        torch.Tensor: 协方差矩阵 (C, C)
    """
    if len(features.shape) == 4:
        # 输入格式: (B, C, H, W)
        B, C, H, W = features.shape
        features_flat = features.view(B, C, -1)  # (B, C, H*W)
        features_reshaped = features_flat.permute(0, 2, 1).reshape(-1, C)  # (B*H*W, C)
    elif len(features.shape) == 3:
        # 输入格式: (N, C, HW)
        N, C, HW = features.shape
        features_reshaped = features.permute(0, 2, 1).reshape(-1, C)  # (N*HW, C)
    else:
        raise ValueError(f"Unsupported feature shape: {features.shape}")
    
    # 中心化特征
    features_centered = features_reshaped - features_reshaped.mean(dim=0, keepdim=True)
    
    # 计算协方差矩阵
    n_samples = features_centered.shape[0]
    cov_matrix = torch.mm(features_centered.T, features_centered) / (n_samples - 1)
    
    return cov_matrix


def covariance_alignment_loss(target_features, source_cov):
    """
    计算协方差对齐损失

    Args:
        target_features (torch.Tensor): 目标域特征 (B, C, H, W)
        source_cov (torch.Tensor or None): 源域协方差矩阵 (C, C)，None时返回零损失

    Returns:
        torch.Tensor: 协方差对齐损失标量
    """
    # 如果源域协方差矩阵不存在，返回零损失
    if source_cov is None:
        return torch.tensor(0.0, device=target_features.device, requires_grad=True)

    # 计算目标域协方差矩阵
    target_cov = compute_covariance_matrix(target_features)

    # 计算Frobenius范数的平方
    cov_diff = target_cov - source_cov
    frobenius_loss = torch.norm(cov_diff, p='fro') ** 2

    return frobenius_loss


def compute_source_covariance_from_model(model, source_dataloader, target_layer_name='layer3', device='cuda'):
    """
    从源域数据计算指定层的协方差矩阵
    
    Args:
        model: 预训练的模型
        source_dataloader: 源域数据加载器
        target_layer_name (str): 目标层名称
        device (str): 计算设备
        
    Returns:
        torch.Tensor: 源域协方差矩阵 (C, C)
    """
    model.eval()
    features_list = []
    
    # 注册hook来提取特征
    target_features = []
    
    def hook_fn(module, input, output):
        target_features.append(output.detach())
    
    # 找到目标层并注册hook
    hook_handle = None
    for name, module in model.named_modules():
        if target_layer_name in name:
            hook_handle = module.register_forward_hook(hook_fn)
            break
    
    if hook_handle is None:
        raise ValueError(f"Layer {target_layer_name} not found in model")
    
    try:
        with torch.no_grad():
            for batch_idx, batch in enumerate(source_dataloader):
                if batch_idx >= 100:  # 限制样本数量以节省计算
                    break
                    
                x = batch['data'].to(device)
                _ = model(x)  # 触发forward和hook
                
                if target_features:
                    features_list.append(target_features[-1])
                    target_features.clear()
        
        # 合并所有批次的特征
        if features_list:
            all_features = torch.cat(features_list, dim=0)  # (N, C, H, W)
            source_cov = compute_covariance_matrix(all_features)
        else:
            raise RuntimeError("No features extracted from source data")
            
    finally:
        # 移除hook
        hook_handle.remove()
    
    return source_cov


def save_source_covariance(source_cov, save_path):
    """
    保存源域协方差矩阵
    
    Args:
        source_cov (torch.Tensor): 源域协方差矩阵
        save_path (str): 保存路径
    """
    os.makedirs(os.path.dirname(save_path), exist_ok=True)
    torch.save(source_cov, save_path)
    print(f"Source covariance matrix saved to: {save_path}")


def load_source_covariance(load_path, device='cuda'):
    """
    加载源域协方差矩阵

    Args:
        load_path (str): 加载路径
        device (str): 目标设备

    Returns:
        torch.Tensor: 源域协方差矩阵
    """
    if not os.path.exists(load_path):
        raise FileNotFoundError(f"Source covariance file not found: {load_path}")

    source_cov = torch.load(load_path, map_location=device)
    print(f"Source covariance matrix loaded from: {load_path}")
    return source_cov


def safe_load_source_covariance(load_path, device='cuda'):
    """
    安全加载源域协方差矩阵，文件不存在时返回None

    Args:
        load_path (str): 协方差矩阵文件路径
        device (str): 目标设备

    Returns:
        torch.Tensor or None: 源域协方差矩阵，文件不存在时返回None
    """
    if not os.path.exists(load_path):
        print(f"WARNING: Source covariance file not found: {load_path}")
        print("Covariance alignment loss will be disabled.")
        return None

    try:
        source_cov = torch.load(load_path, map_location=device)
        print(f"Source covariance matrix loaded from: {load_path}")
        return source_cov
    except Exception as e:
        print(f"WARNING: Failed to load source covariance file: {e}")
        print("Covariance alignment loss will be disabled.")
        return None


def create_source_covariance_if_needed(model, source_dataloader, covariance_path, 
                                     target_layer_name='layer3', device='cuda', force_recompute=False):
    """
    如果需要，创建源域协方差矩阵
    
    Args:
        model: 预训练模型
        source_dataloader: 源域数据加载器
        covariance_path (str): 协方差矩阵保存路径
        target_layer_name (str): 目标层名称
        device (str): 计算设备
        force_recompute (bool): 是否强制重新计算
        
    Returns:
        torch.Tensor: 源域协方差矩阵
    """
    if os.path.exists(covariance_path) and not force_recompute:
        print(f"Loading existing source covariance from: {covariance_path}")
        return load_source_covariance(covariance_path, device)
    else:
        print(f"Computing source covariance for layer: {target_layer_name}")
        source_cov = compute_source_covariance_from_model(
            model, source_dataloader, target_layer_name, device
        )
        save_source_covariance(source_cov, covariance_path)
        return source_cov


def validate_covariance_matrix(cov_matrix, expected_channels):
    """
    验证协方差矩阵的有效性
    
    Args:
        cov_matrix (torch.Tensor): 协方差矩阵
        expected_channels (int): 期望的通道数
        
    Returns:
        bool: 是否有效
    """
    if cov_matrix.shape != (expected_channels, expected_channels):
        print(f"Invalid covariance matrix shape: {cov_matrix.shape}, expected: ({expected_channels}, {expected_channels})")
        return False
    
    # 检查是否为对称矩阵
    if not torch.allclose(cov_matrix, cov_matrix.T, atol=1e-6):
        print("Covariance matrix is not symmetric")
        return False
    
    # 检查是否为半正定矩阵
    eigenvals = torch.linalg.eigvals(cov_matrix)
    if torch.any(eigenvals.real < -1e-6):
        print("Covariance matrix is not positive semi-definite")
        return False
    
    return True
