import os
import sys
import datetime
import torch


class CoDGPDebugger:
    """
    Co-DGP综合调试系统

    提供全面的Co-DGP框架监控，包括：
    - 损失函数分析
    - 协方差矩阵健康检查
    - GAT注意力模式监控
    - 特征分布变化跟踪
    - 智能异常检测和健康评分
    """
    
    def __init__(self, enabled=False, log_file=None):
        """
        初始化调试器
        
        Args:
            enabled (bool): 是否启用调试输出
            log_file (str): 调试日志文件路径，如果为None则输出到控制台
        """
        self.enabled = enabled
        self.log_file = log_file
        self.log_handle = None
        
        if self.enabled and self.log_file:
            # 创建调试日志文件
            log_dir = os.path.dirname(self.log_file)
            if log_dir and not os.path.exists(log_dir):
                os.makedirs(log_dir)
            self.log_handle = open(self.log_file, 'w')
            self._write_header()
    
    def _write_header(self):
        """写入调试日志头部信息"""
        if self.enabled:
            header = f"=== Co-DGP Comprehensive Debug Log Started at {datetime.datetime.now()} ==="
            self._write_line(header)
    
    def _write_line(self, message):
        """写入一行调试信息"""
        if not self.enabled:
            return
            
        if self.log_handle:
            self.log_handle.write(message + '\n')
            self.log_handle.flush()
        else:
            print(message)
    
    def monitor_covariance_health(self, target_cov, source_cov):
        """
        监控协方差矩阵健康状态

        Args:
            target_cov (torch.Tensor): 目标域协方差矩阵
            source_cov (torch.Tensor): 源域协方差矩阵

        Returns:
            dict: 协方差健康指标
        """
        # 计算行列式（奇异性检查）
        target_det = torch.det(target_cov).item()
        source_det = torch.det(source_cov).item()

        # 计算条件数（数值稳定性）
        target_cond = torch.linalg.cond(target_cov).item()

        # 计算Frobenius范数
        target_frob = torch.norm(target_cov, p='fro').item()

        # 对称性检查
        symmetry_error = torch.norm(target_cov - target_cov.T).item()

        # 特征值分析
        eigenvals = torch.linalg.eigvals(target_cov).real
        min_eigenval = eigenvals.min().item()
        max_eigenval = eigenvals.max().item()

        return {
            'target_det': target_det,
            'source_det': source_det,
            'condition_number': target_cond,
            'frobenius_norm': target_frob,
            'symmetry_error': symmetry_error,
            'min_eigenval': min_eigenval,
            'max_eigenval': max_eigenval,
            'eigenval_ratio': max_eigenval / max(min_eigenval, 1e-8)
        }
    
    def monitor_attention_patterns(self, attention_weights_list):
        """
        监控GAT注意力模式

        Args:
            attention_weights_list (list): GAT各层注意力权重列表

        Returns:
            dict: 注意力模式指标
        """
        if not attention_weights_list or attention_weights_list[0] is None:
            return {
                'heads_variance': [0.0],
                'attention_entropy': 0.0,
                'max_attention_weight': 0.0,
                'attention_concentration': 0.0
            }

        # 分析第一层注意力权重（最重要）
        attention = attention_weights_list[0]  # (N, N)

        # 计算注意力熵（集中度指标）
        attention_flat = attention.flatten()
        attention_entropy = -torch.sum(attention_flat * torch.log(attention_flat + 1e-8)).item()

        # 最大注意力权重
        max_weight = attention.max().item()

        # 注意力集中度（最大权重占比）
        concentration = max_weight / attention.sum().item()

        # 多头方差（如果有多头）
        heads_variance = [attention.var().item()]

        return {
            'heads_variance': heads_variance,
            'attention_entropy': attention_entropy,
            'max_attention_weight': max_weight,
            'attention_concentration': concentration
        }

    def monitor_feature_distribution(self, pre_features, post_features):
        """
        监控特征分布变化

        Args:
            pre_features (torch.Tensor): Co-DGP调制前特征
            post_features (torch.Tensor): Co-DGP调制后特征

        Returns:
            dict: 特征分布指标
        """
        # 计算标准差变化
        pre_std = pre_features.std().item()
        post_std = post_features.std().item()
        std_change_ratio = post_std / max(pre_std, 1e-8)

        # 计算均值偏移
        pre_mean = pre_features.mean().item()
        post_mean = post_features.mean().item()
        mean_shift = abs(post_mean - pre_mean)

        # 计算分布偏移幅度
        shift_magnitude = torch.norm(post_features.mean(dim=(0, 2, 3)) -
                                    pre_features.mean(dim=(0, 2, 3))).item()

        # 特征范围变化
        pre_range = (pre_features.max() - pre_features.min()).item()
        post_range = (post_features.max() - post_features.min()).item()
        range_change_ratio = post_range / max(pre_range, 1e-8)

        return {
            'pre_std': pre_std,
            'post_std': post_std,
            'std_change_ratio': std_change_ratio,
            'mean_shift': mean_shift,
            'shift_magnitude': shift_magnitude,
            'range_change_ratio': range_change_ratio
        }

    def detect_anomalies(self, metrics_dict):
        """
        检测各种异常情况

        Args:
            metrics_dict (dict): 所有监控指标

        Returns:
            list: 异常警告列表
        """
        warnings = []

        # 协方差矩阵异常检测
        cov_metrics = metrics_dict.get('covariance', {})
        if cov_metrics.get('target_det', 1.0) < 1e-8:
            warnings.append("⚠️ CRITICAL: 协方差矩阵接近奇异")
        if cov_metrics.get('condition_number', 1.0) > 1000:
            warnings.append("⚠️ WARNING: 协方差矩阵条件数过大")
        if cov_metrics.get('symmetry_error', 0.0) > 1e-5:
            warnings.append("⚠️ WARNING: 协方差矩阵对称性误差较大")

        # 梯度异常检测
        grad_metrics = metrics_dict.get('gradients', {})
        grad_norm = grad_metrics.get('norm', 0.0)
        if grad_norm > 10.0:
            warnings.append("⚠️ CRITICAL: 梯度爆炸")
        elif grad_norm < 1e-6:
            warnings.append("⚠️ WARNING: 梯度消失")

        # 注意力异常检测
        attention_metrics = metrics_dict.get('attention', {})
        if attention_metrics.get('max_attention_weight', 0.0) > 0.8:
            warnings.append("⚠️ WARNING: 注意力过度集中")
        if attention_metrics.get('attention_entropy', 10.0) < 1.0:
            warnings.append("⚠️ INFO: 注意力多样性不足")

        # 特征分布异常检测
        feature_metrics = metrics_dict.get('features', {})
        if feature_metrics.get('std_change_ratio', 1.0) > 3.0:
            warnings.append("⚠️ WARNING: 特征标准差变化过大")
        if feature_metrics.get('shift_magnitude', 0.0) > 1.0:
            warnings.append("⚠️ INFO: 特征分布偏移较大")

        return warnings

    def compute_health_score(self, metrics_dict):
        """
        计算综合健康评分

        Args:
            metrics_dict (dict): 所有监控指标

        Returns:
            float: 健康评分 (0-10)
        """
        score = 10.0

        # 协方差健康评分 (权重: 30%)
        cov_metrics = metrics_dict.get('covariance', {})
        cov_det = cov_metrics.get('target_det', 1.0)
        cov_cond = cov_metrics.get('condition_number', 1.0)

        if cov_det < 1e-8:
            score -= 3.0  # 严重扣分
        elif cov_det < 1e-6:
            score -= 1.5

        if cov_cond > 1000:
            score -= 2.0
        elif cov_cond > 100:
            score -= 1.0

        # 梯度健康评分 (权重: 25%)
        grad_metrics = metrics_dict.get('gradients', {})
        grad_norm = grad_metrics.get('norm', 0.0)

        if grad_norm > 10.0 or grad_norm < 1e-6:
            score -= 2.5
        elif grad_norm > 5.0 or grad_norm < 1e-5:
            score -= 1.0

        # 注意力健康评分 (权重: 25%)
        attention_metrics = metrics_dict.get('attention', {})
        max_attention = attention_metrics.get('max_attention_weight', 0.0)
        attention_entropy = attention_metrics.get('attention_entropy', 2.0)

        if max_attention > 0.8:
            score -= 1.5
        if attention_entropy < 1.0:
            score -= 1.0

        # 特征分布健康评分 (权重: 20%)
        feature_metrics = metrics_dict.get('features', {})
        std_ratio = feature_metrics.get('std_change_ratio', 1.0)

        if std_ratio > 3.0 or std_ratio < 0.3:
            score -= 1.0

        return max(0.0, min(10.0, score))

    def log_comprehensive_debug(self, sample_idx, iter_idx, losses_dict,
                              modulation_dict, covariance_dict, attention_dict,
                              gradients_dict, features_dict, metrics_dict):
        """
        记录综合调试信息

        Args:
            sample_idx (int): 样本索引
            iter_idx (int): 迭代索引
            losses_dict (dict): 损失信息
            modulation_dict (dict): 调制参数信息
            covariance_dict (dict): 协方差矩阵信息
            attention_dict (dict): 注意力模式信息
            gradients_dict (dict): 梯度信息
            features_dict (dict): 特征分布信息
            metrics_dict (dict): 性能指标信息
        """
        if not self.enabled:
            return

        # 构建所有指标字典
        all_metrics = {
            'losses': losses_dict,
            'modulation': modulation_dict,
            'covariance': covariance_dict,
            'attention': attention_dict,
            'gradients': gradients_dict,
            'features': features_dict,
            'metrics': metrics_dict
        }

        # 检测异常
        warnings = self.detect_anomalies(all_metrics)

        # 计算健康评分
        health_score = self.compute_health_score(all_metrics)

        # 格式化输出
        sample_str = f"SAMPLE_{sample_idx:03d}"
        iter_str = f"ITER_{iter_idx}"

        # 主标题
        self._write_line(f"[{sample_str}] [{iter_str}] === Co-DGP Comprehensive Debug ===")

        # 损失信息
        bn_loss = losses_dict.get('bn_loss', 0.0)
        cov_loss = losses_dict.get('cov_loss', 0.0)
        ent_loss = losses_dict.get('entropy_loss', 0.0)
        total_loss = losses_dict.get('total_loss', 0.0)
        self._write_line(f"├─ LOSSES: BN={bn_loss:.4f} | COV={cov_loss:.4f} | ENT={ent_loss:.4f} | TOTAL={total_loss:.4f}")

        # 调制参数信息
        gamma_mean = modulation_dict.get('gamma_mean', 0.0)
        gamma_std = modulation_dict.get('gamma_std', 0.0)
        gamma_min = modulation_dict.get('gamma_min', 0.0)
        gamma_max = modulation_dict.get('gamma_max', 0.0)
        beta_mean = modulation_dict.get('beta_mean', 0.0)
        beta_std = modulation_dict.get('beta_std', 0.0)
        beta_min = modulation_dict.get('beta_min', 0.0)
        beta_max = modulation_dict.get('beta_max', 0.0)
        self._write_line(f"├─ MODULATION: γ(μ={gamma_mean:.3f},σ={gamma_std:.3f},range=[{gamma_min:.3f},{gamma_max:.3f}]) | β(μ={beta_mean:.3f},σ={beta_std:.3f},range=[{beta_min:.3f},{beta_max:.3f}])")

        # 协方差信息
        det_val = covariance_dict.get('target_det', 0.0)
        cond_num = covariance_dict.get('condition_number', 0.0)
        frob_norm = covariance_dict.get('frobenius_norm', 0.0)
        sym_err = covariance_dict.get('symmetry_error', 0.0)
        self._write_line(f"├─ COVARIANCE: det={det_val:.2e} | cond={cond_num:.1f} | frob_norm={frob_norm:.2f} | symmetry_err={sym_err:.2e}")

        # 注意力信息
        heads_var = attention_dict.get('heads_variance', [0.0])
        att_entropy = attention_dict.get('attention_entropy', 0.0)
        max_weight = attention_dict.get('max_attention_weight', 0.0)
        self._write_line(f"├─ ATTENTION: heads_var={heads_var} | entropy={att_entropy:.2f} | max_weight={max_weight:.3f}")

        # 梯度信息
        grad_norm = gradients_dict.get('norm', 0.0)
        grad_status = gradients_dict.get('status', 'UNKNOWN')
        max_grad = gradients_dict.get('max_grad', 0.0)
        self._write_line(f"├─ GRADIENTS: norm={grad_norm:.4f} | status={grad_status} | max_grad={max_grad:.4f}")

        # 特征信息
        pre_std = features_dict.get('pre_std', 0.0)
        post_std = features_dict.get('post_std', 0.0)
        shift_mag = features_dict.get('shift_magnitude', 0.0)
        self._write_line(f"├─ FEATURES: pre_std={pre_std:.3f} | post_std={post_std:.3f} | shift_magnitude={shift_mag:.3f}")

        # 性能指标
        disc_dice = metrics_dict.get('disc_dice', 0.0)
        cup_dice = metrics_dict.get('cup_dice', 0.0)
        self._write_line(f"├─ METRICS: Disc_Dice={disc_dice:.1f} | Cup_Dice={cup_dice:.1f}")

        # 健康状态
        health_status = "✓ NORMAL" if health_score >= 7.0 else "⚠️ WARNING" if health_score >= 4.0 else "❌ CRITICAL"
        self._write_line(f"└─ HEALTH: {health_status} (score: {health_score:.1f}/10)")

        # 异常警告
        if warnings:
            self._write_line("   WARNINGS:")
            for warning in warnings:
                self._write_line(f"   {warning}")

        self._write_line("")  # 空行分隔

    def close(self):
        """关闭调试器，清理资源"""
        if self.log_handle:
            self._write_line(f"=== Co-DGP Comprehensive Debug Log Ended at {datetime.datetime.now()} ===")
            self.log_handle.close()
            self.log_handle = None

    def __del__(self):
        """析构函数，确保文件句柄被正确关闭"""
        self.close()
