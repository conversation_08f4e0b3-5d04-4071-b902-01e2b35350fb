#!/usr/bin/env python3
"""
简单的导入测试 - 验证所有模块可以正确导入
"""

def test_imports():
    """测试关键模块导入"""
    print("Testing module imports...")
    
    try:
        # 测试核心模块导入
        from utils.codgp_module import CoDGPModule
        print("✓ CoDGPModule import successful")
        
        from utils.covariance_utils import compute_covariance_matrix, covariance_alignment_loss
        print("✓ Covariance utils import successful")
        
        from utils.graph_attention import GAT
        print("✓ GAT import successful")
        
        from utils.metrics import calculate_metrics
        print("✓ Metrics import successful")
        
        from utils.loss_debugger import LossDebugger
        print("✓ LossDebugger import successful")
        
        print("\n🎉 All imports successful!")
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False


if __name__ == '__main__':
    test_imports()
