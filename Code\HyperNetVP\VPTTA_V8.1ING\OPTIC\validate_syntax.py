#!/usr/bin/env python3
"""
语法验证脚本 - 验证所有修改后文件的语法正确性
"""

import ast
import os
import sys


def validate_python_syntax(file_path):
    """验证Python文件语法"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 尝试解析AST
        ast.parse(content)
        return True, None
        
    except SyntaxError as e:
        return False, f"Syntax error: {e}"
    except Exception as e:
        return False, f"Error reading file: {e}"


def main():
    """主验证函数"""
    print("Validating Python syntax for modified files...\n")
    
    # 需要验证的文件列表
    files_to_check = [
        'vptta.py',
        'utils/metrics.py',
        'utils/loss_debugger.py',
        'utils/codgp_module.py',
        'utils/covariance_utils.py',
        'utils/graph_attention.py',
        'networks/ResUnet_TTA.py',
        'precompute_source_covariance.py',
        'test_codgp.py'
    ]
    
    base_path = os.path.dirname(os.path.abspath(__file__))
    all_valid = True
    
    for file_path in files_to_check:
        full_path = os.path.join(base_path, file_path)
        
        if not os.path.exists(full_path):
            print(f"⚠️  File not found: {file_path}")
            continue
            
        is_valid, error_msg = validate_python_syntax(full_path)
        
        if is_valid:
            print(f"✓ {file_path}: Syntax OK")
        else:
            print(f"✗ {file_path}: {error_msg}")
            all_valid = False
    
    print(f"\n{'='*50}")
    if all_valid:
        print("🎉 All files passed syntax validation!")
    else:
        print("❌ Some files have syntax errors!")
        sys.exit(1)


if __name__ == '__main__':
    main()
